#!/usr/bin/env python3
"""
Startup script for the Flask LLM Knowledge Assistant API
"""

import os
import sys
from app import create_app
from app.services.vector_store import vector_store

def check_environment():
    """Check if all required environment variables are set"""
    required_vars = ['OPENAI_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease set these variables in your .env file or environment.")
        return False
    
    return True

def initialize_vector_store():
    """Initialize or load existing vector store"""
    try:
        app = create_app()
        with app.app_context():
            vector_db_path = app.config['VECTOR_DB_PATH']
            
            # Try to load existing index
            if vector_store.load_index(vector_db_path):
                stats = vector_store.get_stats()
                print(f"✅ Loaded existing vector store with {stats['total_documents']} documents")
            else:
                print("📝 Initialized new vector store")
        
        return True
    except Exception as e:
        print(f"❌ Error initializing vector store: {e}")
        return False

def main():
    """Main startup function"""
    print("🚀 Starting Flask LLM Knowledge Assistant API")
    print("=" * 50)
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Initialize vector store
    if not initialize_vector_store():
        sys.exit(1)
    
    # Create and run app
    try:
        app = create_app()
        
        print("\n📡 API Endpoints:")
        print("   - Health Check: GET /health")
        print("   - Upload Document: POST /upload-document")
        print("   - Ask Question: POST /ask-question")
        print("   - Vector Store Stats: GET /vector-store/stats")
        print("   - Clear Knowledge Base: DELETE /clear-knowledge-base")
        
        print(f"\n🌐 Starting server on http://localhost:5000")
        print("Press Ctrl+C to stop the server")
        print("=" * 50)
        
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
