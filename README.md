# Flask LLM-Powered Knowledge Assistant API

A Flask-based backend system that powers a Knowledge Assistant API using LLM (OpenAI) to answer user questions based on uploaded documents. The system uses Retrieval-Augmented Generation (RAG) to reduce hallucinations and improve response relevance.

## Features

- **Document Processing**: Support for PDF, DOCX, Markdown, and text files
- **Vector Database**: FAISS-based similarity search for document retrieval
- **LLM Integration**: OpenAI GPT integration with RAG approach
- **Response Optimization**: Prompt engineering and hallucination reduction
- **RESTful API**: Clean API endpoints for document upload and question answering
- **Source Attribution**: Responses include source document references

## Tech Stack

- **Backend**: Flask + Flask-RESTful
- **LLM**: OpenAI GPT-3.5-turbo/GPT-4
- **Vector DB**: FAISS with Sentence Transformers
- **Document Processing**: PyPDF2, python-docx, markdown
- **Text Processing**: LangChain text splitters

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd flask-llm-knowledge-assistant
```

2. Create virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up environment variables:
```bash
cp .env.example .env
# Edit .env and add your OpenAI API key
```

5. Run the application:
```bash
python app.py
```

The API will be available at `http://localhost:5000`

## API Endpoints

### Health Check
```
GET /health
```
Returns API status and vector store statistics.

### Document Upload
```
POST /upload-document
Content-Type: multipart/form-data

Form data:
- file: Document file (PDF, DOCX, MD, TXT)
```

Example response:
```json
{
  "message": "Document uploaded and processed successfully",
  "file_info": {
    "filename": "document.pdf",
    "file_size": 1024,
    "file_type": "pdf"
  },
  "chunks_created": 5,
  "vector_store_stats": {
    "total_documents": 5,
    "index_size": 5
  }
}
```

### Ask Question
```
POST /ask-question
Content-Type: application/json

{
  "question": "What is the use of mitochondria?",
  "top_k": 5  // optional, default: 5
}
```

Example response:
```json
{
  "answer": "The mitochondria is known as the powerhouse of the cell...",
  "sources": ["Document.pdf - Chunk 1", "Document.pdf - Chunk 3"],
  "context_used": 2,
  "model_used": "gpt-3.5-turbo",
  "validation": {
    "hallucination_risk": "low",
    "confidence_score": 0.9
  }
}
```

### Vector Store Operations
```
GET /vector-store/stats
POST /vector-store/search
DELETE /clear-knowledge-base
```

## Configuration

Key configuration options in `config.py`:

- `OPENAI_API_KEY`: Your OpenAI API key
- `LLM_MODEL`: Model to use (default: gpt-3.5-turbo)
- `CHUNK_SIZE`: Text chunk size for processing (default: 1000)
- `CHUNK_OVERLAP`: Overlap between chunks (default: 200)
- `TOP_K_RESULTS`: Number of similar chunks to retrieve (default: 5)

## Usage Examples

### 1. Upload a document
```bash
curl -X POST -F "file=@science_textbook.pdf" http://localhost:5000/upload-document
```

### 2. Ask a question
```bash
curl -X POST -H "Content-Type: application/json" \
  -d '{"question": "What is photosynthesis?"}' \
  http://localhost:5000/ask-question
```

### 3. Check system status
```bash
curl http://localhost:5000/health
```

## Testing

Run tests using pytest:
```bash
pytest tests/ -v
```

## Project Structure

```
├── app/
│   ├── __init__.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── resources.py
│   │   └── routes.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── llm_service.py
│   │   ├── prompt_optimizer.py
│   │   └── vector_store.py
│   └── utils/
│       ├── __init__.py
│       └── document_processor.py
├── tests/
│   ├── __init__.py
│   └── test_api.py
├── uploads/          # Uploaded documents
├── vector_db/        # Vector database storage
├── app.py           # Main application
├── config.py        # Configuration
├── requirements.txt
└── README.md
```

## Features in Detail

### Document Processing
- Supports multiple file formats (PDF, DOCX, MD, TXT)
- Intelligent text chunking with overlap
- Metadata preservation for source attribution

### Vector Search
- FAISS-based similarity search
- Sentence transformer embeddings
- Configurable retrieval parameters

### Response Optimization
- Context optimization and ranking
- Question type detection
- Hallucination risk assessment
- Source citation enhancement

### Error Handling
- Comprehensive error responses
- Input validation
- Graceful fallbacks

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License
