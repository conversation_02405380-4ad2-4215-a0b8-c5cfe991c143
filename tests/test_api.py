import pytest
import json
import os
import tempfile
from app import create_app
from app.services.vector_store import vector_store

@pytest.fixture
def app():
    """Create test app"""
    app = create_app('development')
    app.config['TESTING'] = True
    app.config['OPENAI_API_KEY'] = 'test-key'
    
    with app.app_context():
        yield app

@pytest.fixture
def client(app):
    """Create test client"""
    return app.test_client()

class TestHealthCheck:
    def test_health_check(self, client):
        """Test health check endpoint"""
        response = client.get('/health')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['status'] == 'healthy'
        assert 'vector_store_stats' in data

class TestDocumentUpload:
    def test_upload_text_file(self, client):
        """Test uploading a text file"""
        # Create a temporary text file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("This is a test document about mitochondria. Mitochondria are the powerhouse of the cell.")
            temp_file = f.name
        
        try:
            with open(temp_file, 'rb') as f:
                response = client.post('/upload-document', 
                                     data={'file': (f, 'test.txt')},
                                     content_type='multipart/form-data')
            
            assert response.status_code == 201
            data = json.loads(response.data)
            assert 'chunks_created' in data
            assert data['chunks_created'] > 0
        finally:
            os.unlink(temp_file)
    
    def test_upload_no_file(self, client):
        """Test upload without file"""
        response = client.post('/upload-document')
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_upload_unsupported_file(self, client):
        """Test upload with unsupported file type"""
        with tempfile.NamedTemporaryFile(suffix='.xyz', delete=False) as f:
            f.write(b"test content")
            temp_file = f.name
        
        try:
            with open(temp_file, 'rb') as f:
                response = client.post('/upload-document',
                                     data={'file': (f, 'test.xyz')},
                                     content_type='multipart/form-data')
            
            assert response.status_code == 400
            data = json.loads(response.data)
            assert 'error' in data
        finally:
            os.unlink(temp_file)

class TestAskQuestion:
    def test_ask_question_no_context(self, client):
        """Test asking question without any documents"""
        # Clear vector store first
        vector_store.clear()
        
        response = client.post('/ask-question',
                             data=json.dumps({'question': 'What is mitochondria?'}),
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'answer' in data
        assert data['context_used'] == 0
    
    def test_ask_question_missing_question(self, client):
        """Test asking question without question field"""
        response = client.post('/ask-question',
                             data=json.dumps({}),
                             content_type='application/json')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_ask_empty_question(self, client):
        """Test asking empty question"""
        response = client.post('/ask-question',
                             data=json.dumps({'question': ''}),
                             content_type='application/json')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data

class TestVectorStore:
    def test_vector_store_stats(self, client):
        """Test vector store stats endpoint"""
        response = client.get('/vector-store/stats')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'total_documents' in data
        assert 'model_name' in data
    
    def test_vector_store_search(self, client):
        """Test vector store search endpoint"""
        response = client.post('/vector-store/search',
                             data=json.dumps({'query': 'test query'}),
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'results' in data
        assert 'query' in data
    
    def test_clear_knowledge_base(self, client):
        """Test clearing knowledge base"""
        response = client.delete('/clear-knowledge-base')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'message' in data
