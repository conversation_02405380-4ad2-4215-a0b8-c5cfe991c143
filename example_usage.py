#!/usr/bin/env python3
"""
Example usage script for the Flask LLM Knowledge Assistant API
"""

import requests
import json
import time
import os

# API base URL
BASE_URL = "http://localhost:5000"

def check_health():
    """Check if the API is running"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ API is healthy")
            print(json.dumps(response.json(), indent=2))
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure it's running on localhost:5000")
        return False

def upload_document(file_path):
    """Upload a document to the knowledge base"""
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    print(f"📤 Uploading document: {file_path}")
    
    with open(file_path, 'rb') as f:
        files = {'file': f}
        response = requests.post(f"{BASE_URL}/upload-document", files=files)
    
    if response.status_code == 201:
        print("✅ Document uploaded successfully")
        print(json.dumps(response.json(), indent=2))
        return True
    else:
        print(f"❌ Upload failed: {response.status_code}")
        print(response.text)
        return False

def ask_question(question):
    """Ask a question to the knowledge assistant"""
    print(f"❓ Asking: {question}")
    
    data = {"question": question}
    response = requests.post(
        f"{BASE_URL}/ask-question",
        headers={'Content-Type': 'application/json'},
        data=json.dumps(data)
    )
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Answer received:")
        print(f"📝 Answer: {result['answer']}")
        print(f"📚 Sources: {result['sources']}")
        print(f"🔍 Context used: {result['context_used']}")
        if 'validation' in result:
            print(f"🎯 Validation: {result['validation']}")
        return True
    else:
        print(f"❌ Question failed: {response.status_code}")
        print(response.text)
        return False

def get_vector_store_stats():
    """Get vector store statistics"""
    response = requests.get(f"{BASE_URL}/vector-store/stats")
    
    if response.status_code == 200:
        print("📊 Vector Store Stats:")
        print(json.dumps(response.json(), indent=2))
        return True
    else:
        print(f"❌ Stats request failed: {response.status_code}")
        return False

def main():
    """Main example workflow"""
    print("🚀 Flask LLM Knowledge Assistant API - Example Usage")
    print("=" * 60)
    
    # 1. Check API health
    if not check_health():
        return
    
    print("\n" + "-" * 40)
    
    # 2. Upload sample document
    sample_doc = "sample_documents/science_class_ix.md"
    if upload_document(sample_doc):
        time.sleep(1)  # Give it a moment to process
    
    print("\n" + "-" * 40)
    
    # 3. Get vector store stats
    get_vector_store_stats()
    
    print("\n" + "-" * 40)
    
    # 4. Ask some questions
    questions = [
        "What is mitochondria?",
        "What is photosynthesis?",
        "What are the types of cells?",
        "What is the function of nucleus?",
        "How does cellular respiration work?"
    ]
    
    for question in questions:
        print(f"\n{'='*20}")
        ask_question(question)
        time.sleep(1)  # Rate limiting
    
    print("\n" + "=" * 60)
    print("🎉 Example usage completed!")

if __name__ == '__main__':
    main()
