from flask import Flask
from flask_restful import Api
from flask_cors import CORS
from flasgger import Swagger
import os
from config import config

def create_app(config_name=None):
    app = Flask(__name__)
    
    # Load configuration
    config_name = config_name or os.getenv('FLASK_ENV', 'default')
    app.config.from_object(config[config_name])
    
    # Initialize extensions
    CORS(app)
    api = Api(app)

    # Initialize Swagger
    swagger_config = {
        "headers": [],
        "specs": [
            {
                "endpoint": 'apispec',
                "route": '/apispec.json',
                "rule_filter": lambda rule: True,
                "model_filter": lambda tag: True,
            }
        ],
        "static_url_path": "/flasgger_static",
        "swagger_ui": True,
        "specs_route": "/swagger/"
    }

    swagger_template = {
        "swagger": "2.0",
        "info": {
            "title": "Knowledge Assistant API",
            "description": "A Flask-based LLM-powered Knowledge Assistant API that allows document upload, processing, and question answering using RAG (Retrieval-Augmented Generation)",
            "version": "1.0.0",
            "contact": {
                "name": "API Support",
                "email": "<EMAIL>"
            }
        },
        "host": "localhost:5001",
        "basePath": "/",
        "schemes": ["http", "https"],
        "consumes": ["application/json", "multipart/form-data"],
        "produces": ["application/json"],
        "tags": [
            {
                "name": "Health",
                "description": "Health check endpoints"
            },
            {
                "name": "Documents",
                "description": "Document management endpoints"
            },
            {
                "name": "Questions",
                "description": "Question answering endpoints"
            },
            {
                "name": "Vector Store",
                "description": "Vector store operations"
            }
        ]
    }

    Swagger(app, config=swagger_config, template=swagger_template)
    
    # Create upload and vector db directories
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(app.config['VECTOR_DB_PATH'], exist_ok=True)
    
    # Register API routes
    from app.api.routes import register_routes
    register_routes(api)
    
    return app
