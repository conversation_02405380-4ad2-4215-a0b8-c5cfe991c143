import openai
from typing import List, Dict, Any, Optional
from flask import current_app
from app.services.vector_store import vector_store
from app.services.prompt_optimizer import prompt_optimizer

class LLMService:
    def __init__(self):
        self.client = None

    def _get_client(self):
        """Get or initialize OpenAI client"""
        if self.client is None:
            api_key = current_app.config.get('OPENAI_API_KEY')
            if not api_key:
                raise ValueError("OpenAI API key not found in configuration")
            self.client = openai.OpenAI(api_key=api_key)
        return self.client
    
    def generate_answer(self, question: str, context_docs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate answer using RAG approach"""

        # Optimize context using prompt optimizer
        context = prompt_optimizer.optimize_context(context_docs)

        # Create enhanced prompt with context and question
        prompt = prompt_optimizer.create_enhanced_rag_prompt(question, context)
        
        try:
            # Call OpenAI API
            client = self._get_client()
            response = client.chat.completions.create(
                model=current_app.config.get('LLM_MODEL', 'gpt-3.5-turbo'),
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=current_app.config.get('MAX_TOKENS', 1000),
                temperature=current_app.config.get('TEMPERATURE', 0.1),
                top_p=0.9,
                frequency_penalty=0.0,
                presence_penalty=0.0
            )
            
            answer = response.choices[0].message.content.strip()

            # Extract sources from context documents
            sources = self._extract_sources(context_docs)

            # Validate response quality
            validation = prompt_optimizer.validate_response(answer, context)

            # Enhance response with sources
            enhanced_answer = prompt_optimizer.enhance_response_with_sources(answer, sources)

            return {
                'answer': enhanced_answer,
                'sources': sources,
                'context_used': len(context_docs),
                'model_used': current_app.config.get('LLM_MODEL', 'gpt-3.5-turbo'),
                'validation': validation
            }
            
        except Exception as e:
            raise Exception(f"Error generating answer: {str(e)}")
    
    def _prepare_context(self, context_docs: List[Dict[str, Any]]) -> str:
        """Prepare context string from retrieved documents"""
        if not context_docs:
            return "No relevant context found."
        
        context_parts = []
        for i, doc in enumerate(context_docs, 1):
            content = doc['content']
            filename = doc['metadata'].get('filename', 'Unknown')
            context_parts.append(f"[Context {i} from {filename}]:\n{content}\n")
        
        return "\n".join(context_parts)
    
    def _create_rag_prompt(self, question: str, context: str) -> str:
        """Create RAG prompt with context and question"""
        return f"""Based on the following context information, please answer the question. If the answer cannot be found in the context, please say so clearly.

Context:
{context}

Question: {question}

Instructions:
- Answer based only on the provided context
- If the context doesn't contain enough information, state this clearly
- Be concise but comprehensive
- Include relevant details from the context
- Do not make up information not present in the context

Answer:"""
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for the LLM"""
        return """You are a helpful knowledge assistant. Your role is to answer questions based strictly on the provided context. 

Key guidelines:
1. Only use information from the provided context
2. If information is not in the context, clearly state this
3. Be accurate and avoid hallucinations
4. Provide clear, well-structured answers
5. Reference the context when appropriate
6. If the question is ambiguous, ask for clarification"""
    
    def _extract_sources(self, context_docs: List[Dict[str, Any]]) -> List[str]:
        """Extract source information from context documents"""
        sources = []
        seen_sources = set()
        
        for doc in context_docs:
            metadata = doc['metadata']
            filename = metadata.get('filename', 'Unknown')
            chunk_index = metadata.get('chunk_index', 0)
            
            # Create source reference
            source = f"{filename} - Chunk {chunk_index + 1}"
            
            if source not in seen_sources:
                sources.append(source)
                seen_sources.add(source)
        
        return sources
    
    def ask_question(self, question: str, top_k: int = None) -> Dict[str, Any]:
        """Main method to ask a question using RAG"""
        if not question.strip():
            raise ValueError("Question cannot be empty")
        
        # Use config default if top_k not specified
        if top_k is None:
            top_k = current_app.config.get('TOP_K_RESULTS', 5)
        
        # Retrieve relevant documents
        context_docs = vector_store.search(question, top_k=top_k)
        
        if not context_docs:
            return prompt_optimizer.create_fallback_response(question)
        
        # Generate answer using retrieved context
        return self.generate_answer(question, context_docs)
    
    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings for texts using OpenAI API"""
        try:
            client = self._get_client()
            response = client.embeddings.create(
                model=current_app.config.get('EMBEDDING_MODEL', 'text-embedding-ada-002'),
                input=texts
            )
            return [item.embedding for item in response.data]
        except Exception as e:
            raise Exception(f"Error getting embeddings: {str(e)}")

# Global LLM service instance
llm_service = LLMService()
