import os
import pickle
import numpy as np
import faiss
from typing import List, Dict, Any, Tuple
from sentence_transformers import SentenceTransformer
from flask import current_app

class VectorStore:
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        self.model_name = model_name
        self.encoder = SentenceTransformer(model_name)
        self.dimension = self.encoder.get_sentence_embedding_dimension()
        self.index = None
        self.documents = []
        self.metadata = []
        
        # Initialize FAISS index
        self._initialize_index()
    
    def _initialize_index(self):
        """Initialize FAISS index"""
        self.index = faiss.IndexFlatIP(self.dimension)  # Inner product for cosine similarity
    
    def add_documents(self, documents: List[Dict[str, Any]]):
        """Add documents to the vector store"""
        texts = [doc['content'] for doc in documents]
        embeddings = self.encoder.encode(texts, convert_to_numpy=True)
        
        # Normalize embeddings for cosine similarity
        faiss.normalize_L2(embeddings)
        
        # Add to FAISS index
        self.index.add(embeddings.astype('float32'))
        
        # Store documents and metadata
        self.documents.extend(texts)
        self.metadata.extend([doc['metadata'] for doc in documents])
    
    def search(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for similar documents"""
        if self.index.ntotal == 0:
            return []
        
        # Encode query
        query_embedding = self.encoder.encode([query], convert_to_numpy=True)
        faiss.normalize_L2(query_embedding)
        
        # Search
        scores, indices = self.index.search(query_embedding.astype('float32'), top_k)
        
        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx != -1:  # Valid index
                results.append({
                    'content': self.documents[idx],
                    'metadata': self.metadata[idx],
                    'score': float(score)
                })
        
        return results
    
    def save_index(self, path: str):
        """Save the vector store to disk"""
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # Save FAISS index
        faiss.write_index(self.index, f"{path}/faiss.index")
        
        # Save documents and metadata
        with open(f"{path}/documents.pkl", 'wb') as f:
            pickle.dump({
                'documents': self.documents,
                'metadata': self.metadata,
                'model_name': self.model_name
            }, f)
    
    def load_index(self, path: str):
        """Load the vector store from disk"""
        try:
            # Load FAISS index
            self.index = faiss.read_index(f"{path}/faiss.index")
            
            # Load documents and metadata
            with open(f"{path}/documents.pkl", 'rb') as f:
                data = pickle.load(f)
                self.documents = data['documents']
                self.metadata = data['metadata']
                
                # Ensure model consistency
                if data.get('model_name') != self.model_name:
                    print(f"Warning: Model mismatch. Stored: {data.get('model_name')}, Current: {self.model_name}")
            
            return True
        except (FileNotFoundError, Exception) as e:
            print(f"Could not load index: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the vector store"""
        return {
            'total_documents': len(self.documents),
            'index_size': self.index.ntotal if self.index else 0,
            'dimension': self.dimension,
            'model_name': self.model_name
        }
    
    def clear(self):
        """Clear all documents and reset index"""
        self._initialize_index()
        self.documents = []
        self.metadata = []

# Global vector store instance
vector_store = VectorStore()
