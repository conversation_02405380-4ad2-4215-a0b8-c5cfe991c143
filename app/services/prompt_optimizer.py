from typing import List, Dict, Any
import re

class PromptOptimizer:
    """<PERSON>les prompt engineering and optimization for better LLM responses"""
    
    def __init__(self):
        self.hallucination_keywords = [
            "i think", "i believe", "probably", "might be", "could be",
            "it seems", "appears to be", "likely", "possibly", "maybe"
        ]
    
    def optimize_context(self, context_docs: List[Dict[str, Any]], max_context_length: int = 3000) -> str:
        """Optimize context by ranking and truncating if necessary"""
        if not context_docs:
            return "No relevant context found."
        
        # Sort by relevance score (higher is better)
        sorted_docs = sorted(context_docs, key=lambda x: x.get('score', 0), reverse=True)
        
        context_parts = []
        current_length = 0
        
        for i, doc in enumerate(sorted_docs, 1):
            content = doc['content']
            filename = doc['metadata'].get('filename', 'Unknown')
            score = doc.get('score', 0)
            
            # Create context entry with relevance info
            context_entry = f"[Context {i} from {filename} (relevance: {score:.3f})]:\n{content}\n"
            
            # Check if adding this context would exceed limit
            if current_length + len(context_entry) > max_context_length and context_parts:
                break
            
            context_parts.append(context_entry)
            current_length += len(context_entry)
        
        return "\n".join(context_parts)
    
    def create_enhanced_rag_prompt(self, question: str, context: str, question_type: str = None) -> str:
        """Create an enhanced RAG prompt with better instructions"""
        
        # Detect question type if not provided
        if not question_type:
            question_type = self._detect_question_type(question)
        
        # Get type-specific instructions
        type_instructions = self._get_type_specific_instructions(question_type)
        
        return f"""You are a knowledgeable assistant answering questions based on provided context. Follow these guidelines strictly:

CONTEXT INFORMATION:
{context}

QUESTION: {question}

INSTRUCTIONS:
1. Answer ONLY based on the provided context above
2. If the context doesn't contain sufficient information, clearly state: "Based on the provided context, I don't have enough information to answer this question."
3. Do not make assumptions or add information not present in the context
4. Be precise and cite specific parts of the context when relevant
5. If multiple sources provide different information, mention this discrepancy
6. {type_instructions}

RESPONSE FORMAT:
- Start with a direct answer if possible
- Support your answer with specific references to the context
- If uncertain, clearly express the limitation

Answer:"""
    
    def _detect_question_type(self, question: str) -> str:
        """Detect the type of question to provide appropriate instructions"""
        question_lower = question.lower()
        
        if any(word in question_lower for word in ['what is', 'define', 'definition', 'meaning']):
            return 'definition'
        elif any(word in question_lower for word in ['how', 'process', 'steps', 'procedure']):
            return 'process'
        elif any(word in question_lower for word in ['why', 'reason', 'cause', 'because']):
            return 'explanation'
        elif any(word in question_lower for word in ['when', 'time', 'date', 'period']):
            return 'temporal'
        elif any(word in question_lower for word in ['where', 'location', 'place']):
            return 'location'
        elif any(word in question_lower for word in ['compare', 'difference', 'versus', 'vs']):
            return 'comparison'
        elif question_lower.startswith(('is', 'are', 'can', 'does', 'do', 'will', 'would')):
            return 'yes_no'
        else:
            return 'general'
    
    def _get_type_specific_instructions(self, question_type: str) -> str:
        """Get specific instructions based on question type"""
        instructions = {
            'definition': "Provide a clear, concise definition using the exact terminology from the context.",
            'process': "Break down the process into clear steps if the context provides sequential information.",
            'explanation': "Explain the reasoning or causation as described in the context, avoiding speculation.",
            'temporal': "Provide specific time information only if explicitly mentioned in the context.",
            'location': "Give location information only if clearly stated in the context.",
            'comparison': "Compare only the aspects that are explicitly discussed in the context for both items.",
            'yes_no': "Provide a clear yes/no answer if the context supports it, otherwise explain what information is available.",
            'general': "Provide a comprehensive answer covering all relevant aspects mentioned in the context."
        }
        return instructions.get(question_type, instructions['general'])
    
    def validate_response(self, response: str, context: str) -> Dict[str, Any]:
        """Validate response for potential hallucinations and quality"""
        validation_results = {
            'hallucination_risk': 'low',
            'confidence_score': 1.0,
            'issues': [],
            'suggestions': []
        }
        
        response_lower = response.lower()
        
        # Check for hallucination indicators
        hallucination_count = sum(1 for keyword in self.hallucination_keywords if keyword in response_lower)
        
        if hallucination_count > 0:
            validation_results['hallucination_risk'] = 'medium' if hallucination_count <= 2 else 'high'
            validation_results['confidence_score'] -= 0.1 * hallucination_count
            validation_results['issues'].append(f"Contains {hallucination_count} uncertainty indicators")
        
        # Check if response acknowledges context limitations
        if "don't have enough information" in response_lower or "not mentioned in the context" in response_lower:
            validation_results['suggestions'].append("Good: Acknowledges context limitations")
        
        # Check for specific context references
        if "according to" in response_lower or "the context states" in response_lower or "mentioned in" in response_lower:
            validation_results['suggestions'].append("Good: References context explicitly")
        
        # Check response length appropriateness
        if len(response) < 20:
            validation_results['issues'].append("Response might be too brief")
        elif len(response) > 1000:
            validation_results['issues'].append("Response might be too verbose")
        
        return validation_results
    
    def enhance_response_with_sources(self, response: str, sources: List[str]) -> str:
        """Enhance response with properly formatted source citations"""
        if not sources:
            return response
        
        # Add sources section
        sources_text = "\n\nSources:\n" + "\n".join(f"• {source}" for source in sources)
        
        return response + sources_text
    
    def create_fallback_response(self, question: str) -> Dict[str, Any]:
        """Create a fallback response when no context is available"""
        return {
            'answer': f"I don't have any relevant information in my knowledge base to answer the question: '{question}'. Please upload relevant documents first, or try rephrasing your question.",
            'sources': [],
            'context_used': 0,
            'confidence': 'low',
            'suggestion': "Try uploading documents related to your question or rephrase the question with different keywords."
        }

# Global prompt optimizer instance
prompt_optimizer = PromptOptimizer()
