import os
import re
from typing import List, Dict, Any
import PyPDF2
import docx
import markdown
from langchain.text_splitter import RecursiveCharacterTextSplitter

class DocumentProcessor:
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", " ", ""]
        )
    
    def process_file(self, file_path: str, filename: str) -> List[Dict[str, Any]]:
        """Process a file and return chunks with metadata"""
        file_extension = filename.lower().split('.')[-1]
        
        if file_extension == 'pdf':
            text = self._extract_pdf_text(file_path)
        elif file_extension == 'docx':
            text = self._extract_docx_text(file_path)
        elif file_extension == 'md':
            text = self._extract_markdown_text(file_path)
        elif file_extension == 'txt':
            text = self._extract_text_file(file_path)
        else:
            raise ValueError(f"Unsupported file type: {file_extension}")
        
        # Clean and preprocess text
        text = self._clean_text(text)
        
        # Split into chunks
        chunks = self.text_splitter.split_text(text)
        
        # Create chunk documents with metadata
        documents = []
        for i, chunk in enumerate(chunks):
            documents.append({
                'content': chunk,
                'metadata': {
                    'filename': filename,
                    'file_path': file_path,
                    'chunk_index': i,
                    'file_type': file_extension,
                    'chunk_size': len(chunk)
                }
            })
        
        return documents
    
    def _extract_pdf_text(self, file_path: str) -> str:
        """Extract text from PDF file"""
        text = ""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page_num, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    text += f"\n--- Page {page_num + 1} ---\n{page_text}"
        except Exception as e:
            raise Exception(f"Error processing PDF: {str(e)}")
        return text
    
    def _extract_docx_text(self, file_path: str) -> str:
        """Extract text from DOCX file"""
        try:
            doc = docx.Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text
        except Exception as e:
            raise Exception(f"Error processing DOCX: {str(e)}")
    
    def _extract_markdown_text(self, file_path: str) -> str:
        """Extract text from Markdown file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                md_content = file.read()
                # Convert markdown to plain text (remove markdown syntax)
                html = markdown.markdown(md_content)
                # Simple HTML tag removal
                text = re.sub('<[^<]+?>', '', html)
                return text
        except Exception as e:
            raise Exception(f"Error processing Markdown: {str(e)}")
    
    def _extract_text_file(self, file_path: str) -> str:
        """Extract text from plain text file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
        except Exception as e:
            raise Exception(f"Error processing text file: {str(e)}")
    
    def _clean_text(self, text: str) -> str:
        """Clean and preprocess text"""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)]', '', text)
        # Remove empty lines
        text = re.sub(r'\n\s*\n', '\n', text)
        return text.strip()
    
    def get_file_info(self, file_path: str, filename: str) -> Dict[str, Any]:
        """Get basic file information"""
        file_stats = os.stat(file_path)
        return {
            'filename': filename,
            'file_size': file_stats.st_size,
            'file_type': filename.lower().split('.')[-1],
            'created_at': file_stats.st_ctime,
            'modified_at': file_stats.st_mtime
        }
