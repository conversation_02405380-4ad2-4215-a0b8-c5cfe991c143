import os
from flask import request, current_app, jsonify
from flask_restful import Resource
from werkzeug.utils import secure_filename
from flasgger import swag_from
from app.utils.document_processor import DocumentProcessor
from app.services.vector_store import vector_store
from app.services.llm_service import llm_service

class HealthCheck(Resource):
    def get(self):
        """Health check endpoint
        ---
        tags:
          - Health
        summary: Check API health status
        description: Returns the health status of the Knowledge Assistant API along with vector store statistics
        responses:
          200:
            description: API is healthy
            schema:
              type: object
              properties:
                status:
                  type: string
                  example: "healthy"
                message:
                  type: string
                  example: "Knowledge Assistant API is running"
                vector_store_stats:
                  type: object
                  properties:
                    total_documents:
                      type: integer
                      example: 5
                    total_chunks:
                      type: integer
                      example: 150
        """
        return {
            'status': 'healthy',
            'message': 'Knowledge Assistant API is running',
            'vector_store_stats': vector_store.get_stats()
        }

class DocumentUpload(Resource):
    def post(self):
        """Upload and process documents
        ---
        tags:
          - Documents
        summary: Upload and process a document
        description: Upload a document file (PDF, DOCX, MD, TXT) and process it into chunks for the knowledge base
        consumes:
          - multipart/form-data
        parameters:
          - name: file
            in: formData
            type: file
            required: true
            description: Document file to upload (PDF, DOCX, MD, TXT)
        responses:
          201:
            description: Document uploaded and processed successfully
            schema:
              type: object
              properties:
                message:
                  type: string
                  example: "Document uploaded and processed successfully"
                file_info:
                  type: object
                  properties:
                    filename:
                      type: string
                      example: "document.pdf"
                    size:
                      type: integer
                      example: 1024000
                    type:
                      type: string
                      example: "pdf"
                chunks_created:
                  type: integer
                  example: 25
                vector_store_stats:
                  type: object
                  properties:
                    total_documents:
                      type: integer
                      example: 5
                    total_chunks:
                      type: integer
                      example: 150
          400:
            description: Bad request (no file, empty file, or unsupported file type)
            schema:
              type: object
              properties:
                error:
                  type: string
                  example: "No file provided"
          500:
            description: Internal server error
            schema:
              type: object
              properties:
                error:
                  type: string
                  example: "Error processing document: ..."
        """
        try:
            # Check if file is present
            if 'file' not in request.files:
                return {'error': 'No file provided'}, 400
            
            file = request.files['file']
            if file.filename == '':
                return {'error': 'No file selected'}, 400
            
            # Check file extension
            if not self._allowed_file(file.filename):
                return {
                    'error': f'File type not supported. Allowed types: {", ".join(current_app.config["ALLOWED_EXTENSIONS"])}'
                }, 400
            
            # Save file
            filename = secure_filename(file.filename)
            file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
            file.save(file_path)
            
            # Process document
            processor = DocumentProcessor(
                chunk_size=current_app.config.get('CHUNK_SIZE', 1000),
                chunk_overlap=current_app.config.get('CHUNK_OVERLAP', 200)
            )
            
            documents = processor.process_file(file_path, filename)
            
            # Add to vector store
            vector_store.add_documents(documents)
            
            # Save vector store
            vector_store.save_index(current_app.config['VECTOR_DB_PATH'])
            
            # Get file info
            file_info = processor.get_file_info(file_path, filename)
            
            return {
                'message': 'Document uploaded and processed successfully',
                'file_info': file_info,
                'chunks_created': len(documents),
                'vector_store_stats': vector_store.get_stats()
            }, 201
            
        except Exception as e:
            return {'error': f'Error processing document: {str(e)}'}, 500
    
    def _allowed_file(self, filename):
        """Check if file extension is allowed"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

class AskQuestion(Resource):
    def post(self):
        """Ask a question and get an answer using RAG
        ---
        tags:
          - Questions
        summary: Ask a question to the knowledge base
        description: Submit a question and get an AI-generated answer based on the uploaded documents using RAG (Retrieval-Augmented Generation)
        consumes:
          - application/json
        parameters:
          - name: body
            in: body
            required: true
            schema:
              type: object
              required:
                - question
              properties:
                question:
                  type: string
                  description: The question to ask
                  example: "What is machine learning?"
                top_k:
                  type: integer
                  description: Number of top relevant chunks to retrieve (optional)
                  default: 5
                  example: 5
        responses:
          200:
            description: Question answered successfully
            schema:
              type: object
              properties:
                question:
                  type: string
                  example: "What is machine learning?"
                answer:
                  type: string
                  example: "Machine learning is a subset of artificial intelligence..."
                sources:
                  type: array
                  items:
                    type: object
                    properties:
                      content:
                        type: string
                        example: "Machine learning algorithms..."
                      metadata:
                        type: object
                        properties:
                          filename:
                            type: string
                            example: "ml_guide.pdf"
                          page:
                            type: integer
                            example: 1
                confidence_score:
                  type: number
                  example: 0.85
          400:
            description: Bad request (missing or empty question)
            schema:
              type: object
              properties:
                error:
                  type: string
                  example: "Question is required"
          500:
            description: Internal server error
            schema:
              type: object
              properties:
                error:
                  type: string
                  example: "Error processing question: ..."
        """
        try:
            data = request.get_json()
            
            if not data or 'question' not in data:
                return {'error': 'Question is required'}, 400
            
            question = data['question'].strip()
            if not question:
                return {'error': 'Question cannot be empty'}, 400
            
            # Optional parameters
            top_k = data.get('top_k', current_app.config.get('TOP_K_RESULTS', 5))
            
            # Get answer using LLM service
            result = llm_service.ask_question(question, top_k=top_k)
            
            return result, 200
            
        except Exception as e:
            return {'error': f'Error processing question: {str(e)}'}, 500

class VectorStoreStats(Resource):
    def get(self):
        """Get vector store statistics
        ---
        tags:
          - Vector Store
        summary: Get vector store statistics
        description: Retrieve statistics about the current state of the vector store including document and chunk counts
        responses:
          200:
            description: Vector store statistics retrieved successfully
            schema:
              type: object
              properties:
                total_documents:
                  type: integer
                  description: Total number of documents in the vector store
                  example: 5
                total_chunks:
                  type: integer
                  description: Total number of text chunks in the vector store
                  example: 150
                index_size:
                  type: integer
                  description: Size of the vector index
                  example: 1024
        """
        return vector_store.get_stats()

class VectorStoreSearch(Resource):
    def post(self):
        """Search vector store directly (for debugging)
        ---
        tags:
          - Vector Store
        summary: Search vector store directly
        description: Perform a direct search on the vector store to find relevant document chunks (useful for debugging and testing)
        consumes:
          - application/json
        parameters:
          - name: body
            in: body
            required: true
            schema:
              type: object
              required:
                - query
              properties:
                query:
                  type: string
                  description: Search query text
                  example: "machine learning algorithms"
                top_k:
                  type: integer
                  description: Number of top results to return
                  default: 5
                  example: 5
        responses:
          200:
            description: Search completed successfully
            schema:
              type: object
              properties:
                query:
                  type: string
                  example: "machine learning algorithms"
                results:
                  type: array
                  items:
                    type: object
                    properties:
                      content:
                        type: string
                        example: "Machine learning algorithms are computational methods..."
                      metadata:
                        type: object
                        properties:
                          filename:
                            type: string
                            example: "ml_guide.pdf"
                          page:
                            type: integer
                            example: 1
                      score:
                        type: number
                        example: 0.85
                total_results:
                  type: integer
                  example: 5
          400:
            description: Bad request (missing or empty query)
            schema:
              type: object
              properties:
                error:
                  type: string
                  example: "Query is required"
          500:
            description: Internal server error
            schema:
              type: object
              properties:
                error:
                  type: string
                  example: "Error searching vector store: ..."
        """
        try:
            data = request.get_json()
            
            if not data or 'query' not in data:
                return {'error': 'Query is required'}, 400
            
            query = data['query'].strip()
            if not query:
                return {'error': 'Query cannot be empty'}, 400
            
            top_k = data.get('top_k', 5)
            
            results = vector_store.search(query, top_k=top_k)
            
            return {
                'query': query,
                'results': results,
                'total_results': len(results)
            }, 200
            
        except Exception as e:
            return {'error': f'Error searching vector store: {str(e)}'}, 500

class ClearKnowledgeBase(Resource):
    def delete(self):
        """Clear all documents from the knowledge base
        ---
        tags:
          - Documents
        summary: Clear the knowledge base
        description: Remove all documents and chunks from the vector store, effectively clearing the entire knowledge base
        responses:
          200:
            description: Knowledge base cleared successfully
            schema:
              type: object
              properties:
                message:
                  type: string
                  example: "Knowledge base cleared successfully"
                vector_store_stats:
                  type: object
                  properties:
                    total_documents:
                      type: integer
                      example: 0
                    total_chunks:
                      type: integer
                      example: 0
          500:
            description: Internal server error
            schema:
              type: object
              properties:
                error:
                  type: string
                  example: "Error clearing knowledge base: ..."
        """
        try:
            vector_store.clear()
            vector_store.save_index(current_app.config['VECTOR_DB_PATH'])
            
            return {
                'message': 'Knowledge base cleared successfully',
                'vector_store_stats': vector_store.get_stats()
            }, 200
            
        except Exception as e:
            return {'error': f'Error clearing knowledge base: {str(e)}'}, 500
