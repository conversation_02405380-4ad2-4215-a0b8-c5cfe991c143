from app.api.resources import (
    HealthCheck,
    DocumentUpload,
    AskQuestion,
    VectorStoreStats,
    VectorStoreSearch,
    ClearKnowledgeBase
)

def register_routes(api):
    """Register all API routes"""
    
    # Health check
    api.add_resource(HealthCheck, '/health')
    
    # Document management
    api.add_resource(DocumentUpload, '/upload-document')
    api.add_resource(ClearKnowledgeBase, '/clear-knowledge-base')
    
    # Question answering
    api.add_resource(AskQuestion, '/ask-question')
    
    # Vector store operations
    api.add_resource(VectorStoreStats, '/vector-store/stats')
    api.add_resource(VectorStoreSearch, '/vector-store/search')
