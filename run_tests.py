#!/usr/bin/env python3
"""
Test runner script for the Flask LLM Knowledge Assistant API
"""

import os
import sys
import subprocess

def run_tests():
    """Run all tests"""
    print("Running Flask LLM Knowledge Assistant API Tests...")
    print("=" * 50)
    
    # Set environment variables for testing
    os.environ['FLASK_ENV'] = 'development'
    os.environ['OPENAI_API_KEY'] = 'test-key-for-testing'
    
    try:
        # Run pytest
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 
            'tests/', 
            '-v', 
            '--tb=short',
            '--color=yes'
        ], check=True)
        
        print("\n" + "=" * 50)
        print("All tests passed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\nTests failed with exit code: {e.returncode}")
        return False
    except FileNotFoundError:
        print("Error: pytest not found. Please install it with: pip install pytest")
        return False

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
